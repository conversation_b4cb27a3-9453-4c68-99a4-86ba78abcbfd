import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { BaseActivityDomainEvent } from '../../../activities/domain/events/BaseActivityDomainEvent'
import { NotificationTarget } from '../../../activities/domain/events/interfaces/IActivityDomainEvent'
import { Role } from '../../types/user'
import { User } from '../user'

export class UserUnassignedEvent extends BaseActivityDomainEvent {
  public static readonly eventIdentifier: string = 'user:unassigned'
  public readonly user: User
  public readonly unassignedBy: UniqueEntityID
  public readonly removedRole: Role
  public readonly removedCollectionId?: UniqueEntityID

  constructor(
    user: User,
    unassignedBy: UniqueEntityID,
    removedRole: Role,
    collectionId: UniqueEntityID,
    removedCollectionId: UniqueEntityID | undefined,
    targetUsers: NotificationTarget[]
  ) {
    const activityData = {
      userId: user.id.value,
      userName: user.name,
      userEmail: user.email.value,
      unassignedBy: unassignedBy.value,
      removedRole,
      removedCollectionId: removedCollectionId?.value
    }

    super(
      UserUnassignedEvent.eventIdentifier,
      'USER_UNASSIGN',
      activityData,
      collectionId,
      targetUsers
    )
    
    this.user = user
    this.unassignedBy = unassignedBy
    this.removedRole = removedRole
    this.removedCollectionId = removedCollectionId
  }

  public getEntityId(): UniqueEntityID {
    return this.user.id
  }
}
