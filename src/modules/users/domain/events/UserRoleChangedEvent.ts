import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { BaseActivityDomainEvent } from '../../../activities/domain/events/BaseActivityDomainEvent'
import { NotificationTarget } from '../../../activities/domain/events/interfaces/IActivityDomainEvent'
import { Role } from '../../types/user'
import { User } from '../user'

export class UserRoleChangedEvent extends BaseActivityDomainEvent {
  public static readonly eventIdentifier: string = 'user:roleChanged'
  public readonly user: User
  public readonly changedBy: UniqueEntityID
  public readonly oldRole: Role
  public readonly newRole: Role
  public readonly roleCollectionId?: UniqueEntityID

  constructor(
    user: User,
    changedBy: UniqueEntityID,
    oldRole: Role,
    newRole: Role,
    collectionId: UniqueEntityID,
    roleCollectionId: UniqueEntityID | undefined,
    targetUsers: NotificationTarget[]
  ) {
    const activityData = {
      userId: user.id.value,
      userName: user.name,
      userEmail: user.email.value,
      changedBy: changedBy.value,
      oldRole,
      newRole,
      roleCollectionId: roleCollectionId?.value
    }

    super(
      UserRoleChangedEvent.eventIdentifier,
      'ROLE_CHANGE',
      activityData,
      collectionId,
      targetUsers
    )
    
    this.user = user
    this.changedBy = changedBy
    this.oldRole = oldRole
    this.newRole = newRole
    this.roleCollectionId = roleCollectionId
  }

  public getEntityId(): UniqueEntityID {
    return this.user.id
  }
}
