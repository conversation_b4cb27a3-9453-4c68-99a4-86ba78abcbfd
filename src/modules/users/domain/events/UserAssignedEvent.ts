import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { BaseActivityDomainEvent } from '../../../activities/domain/events/BaseActivityDomainEvent'
import { NotificationTarget } from '../../../activities/domain/events/interfaces/IActivityDomainEvent'
import { Role } from '../../types/user'
import { User } from '../user'

export class UserAssignedEvent extends BaseActivityDomainEvent {
  public static readonly eventIdentifier: string = 'user:assigned'
  public readonly user: User
  public readonly assignedBy: UniqueEntityID
  public readonly assignedRole: Role
  public readonly assignedCollectionId?: UniqueEntityID

  constructor(
    user: User,
    assignedBy: UniqueEntityID,
    assignedRole: Role,
    collectionId: UniqueEntityID,
    assignedCollectionId: UniqueEntityID | undefined,
    targetUsers: NotificationTarget[]
  ) {
    const activityData = {
      userId: user.id.value,
      userName: user.name,
      userEmail: user.email.value,
      assignedBy: assignedBy.value,
      assignedRole,
      assignedCollectionId: assignedCollectionId?.value,
      isNewUser: !user.createdAt || user.createdAt.getTime() > Date.now() - 5000 // Created within last 5 seconds
    }

    super(
      UserAssignedEvent.eventIdentifier,
      'USER_ASSIGN',
      activityData,
      collectionId,
      targetUsers
    )
    
    this.user = user
    this.assignedBy = assignedBy
    this.assignedRole = assignedRole
    this.assignedCollectionId = assignedCollectionId
  }

  public getEntityId(): UniqueEntityID {
    return this.user.id
  }
}
