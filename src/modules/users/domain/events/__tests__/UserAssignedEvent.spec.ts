import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotificationTarget } from '../../../../activities/domain/events/interfaces/IActivityDomainEvent'
import { stubUser } from '../../__stubs__/user.stub'
import { UserAssignedEvent } from '../UserAssignedEvent'

describe('UserAssignedEvent', () => {
  it('should create a UserAssignedEvent with correct properties', () => {
    const user = stubUser()
    const assignedBy = new UniqueEntityID()
    const collectionId = new UniqueEntityID()
    const assignedCollectionId = new UniqueEntityID()
    const targetUsers: NotificationTarget[] = [
      {
        userId: new UniqueEntityID(),
        reason: 'COLLECTION_MANAGER'
      },
      {
        userId: user.id,
        reason: 'ASSIGNED_USER'
      }
    ]

    const event = new UserAssignedEvent(
      user,
      assignedBy,
      'EDITOR',
      collectionId,
      assignedCollectionId,
      targetUsers
    )

    expect(event.eventName).toBe(UserAssignedEvent.eventIdentifier)
    expect(event.activityType).toBe('USER_ASSIGN')
    expect(event.collectionId).toBe(collectionId)
    expect(event.targetUsers).toEqual(targetUsers)
    expect(event.user).toBe(user)
    expect(event.assignedBy).toBe(assignedBy)
    expect(event.assignedRole).toBe('EDITOR')
    expect(event.assignedCollectionId).toBe(assignedCollectionId)
    expect(event.getEntityId()).toBe(user.id)
  })

  it('should include correct activity data', () => {
    const user = stubUser()
    const assignedBy = new UniqueEntityID()
    const collectionId = new UniqueEntityID()
    const assignedCollectionId = new UniqueEntityID()
    const targetUsers: NotificationTarget[] = []

    const event = new UserAssignedEvent(
      user,
      assignedBy,
      'CONTRIBUTOR',
      collectionId,
      assignedCollectionId,
      targetUsers
    )

    expect(event.activityData).toEqual({
      userId: user.id.value,
      userName: user.name,
      userEmail: user.email.value,
      assignedBy: assignedBy.value,
      assignedRole: 'CONTRIBUTOR',
      assignedCollectionId: assignedCollectionId.value,
      isNewUser: expect.any(Boolean)
    })
  })

  it('should have correct event identifier', () => {
    expect(UserAssignedEvent.eventIdentifier).toBe('user:assigned')
  })
})
