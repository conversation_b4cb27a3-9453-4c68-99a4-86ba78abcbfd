import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { NotificationTargetService } from '../../activities/services/NotificationTargetService'
import { User } from '../domain/user'
import { UserAssignedEvent, UserRoleChangedEvent, UserUnassignedEvent } from '../domain/events'
import { Role } from '../types/user'

/**
 * Service to handle user assignment-related domain events
 */
export class UserAssignmentEventService {
  constructor(private readonly notificationTargetService: NotificationTargetService) {}

  /**
   * Trigger UserAssignedEvent when a user is assigned to a collection
   * Notifies collection managers and the assigned user
   */
  async triggerUserAssigned(
    user: User,
    assignedBy: UniqueEntityID,
    assignedRole: Role,
    assignedCollectionId?: UniqueEntityID
  ): Promise<void> {
    // For platform roles, use a dummy collection ID for activity tracking
    const activityCollectionId = assignedCollectionId || new UniqueEntityID()

    let targetUsers = []

    if (assignedCollectionId) {
      // Collection assignment - notify collection managers (excluding the assigner)
      const collectionTargets = await this.notificationTargetService.getCollectionNotificationTargets(
        assignedCollectionId,
        assignedBy
      )
      targetUsers.push(...collectionTargets)
    } else {
      // Platform assignment - notify platform managers (excluding the assigner)
      const platformTargets = await this.notificationTargetService.getPlatformManagerTargets(
        assignedBy
      )
      targetUsers.push(...platformTargets)
    }

    // Also notify the assigned user
    const userTargets = await this.notificationTargetService.getSpecificUserTarget(
      user.id,
      'ASSIGNED_USER'
    )
    targetUsers.push(...userTargets)

    const event = new UserAssignedEvent(
      user,
      assignedBy,
      assignedRole,
      activityCollectionId,
      assignedCollectionId,
      targetUsers
    )
    user.addDomainEvent(event)
  }

  /**
   * Trigger UserRoleChangedEvent when a user's role is changed
   * Notifies collection managers and the affected user
   */
  async triggerUserRoleChanged(
    user: User,
    changedBy: UniqueEntityID,
    oldRole: Role,
    newRole: Role,
    roleCollectionId?: UniqueEntityID
  ): Promise<void> {
    // For platform roles, use a dummy collection ID for activity tracking
    const activityCollectionId = roleCollectionId || new UniqueEntityID()

    let targetUsers = []

    if (roleCollectionId) {
      // Collection role change - notify collection managers (excluding the changer)
      const collectionTargets = await this.notificationTargetService.getCollectionNotificationTargets(
        roleCollectionId,
        changedBy
      )
      targetUsers.push(...collectionTargets)
    } else {
      // Platform role change - notify platform managers (excluding the changer)
      const platformTargets = await this.notificationTargetService.getPlatformManagerTargets(
        changedBy
      )
      targetUsers.push(...platformTargets)
    }

    // Also notify the affected user
    const userTargets = await this.notificationTargetService.getSpecificUserTarget(
      user.id,
      'ASSIGNED_USER'
    )
    targetUsers.push(...userTargets)

    const event = new UserRoleChangedEvent(
      user,
      changedBy,
      oldRole,
      newRole,
      activityCollectionId,
      roleCollectionId,
      targetUsers
    )
    user.addDomainEvent(event)
  }

  /**
   * Trigger UserUnassignedEvent when a user is removed from a collection
   * Notifies collection managers and the unassigned user
   */
  async triggerUserUnassigned(
    user: User,
    unassignedBy: UniqueEntityID,
    removedRole: Role,
    removedCollectionId?: UniqueEntityID
  ): Promise<void> {
    // For platform roles, use a dummy collection ID for activity tracking
    const activityCollectionId = removedCollectionId || new UniqueEntityID()

    let targetUsers = []

    if (removedCollectionId) {
      // Collection unassignment - notify collection managers (excluding the unassigner)
      const collectionTargets = await this.notificationTargetService.getCollectionNotificationTargets(
        removedCollectionId,
        unassignedBy
      )
      targetUsers.push(...collectionTargets)
    } else {
      // Platform unassignment - notify platform managers (excluding the unassigner)
      const platformTargets = await this.notificationTargetService.getPlatformManagerTargets(
        unassignedBy
      )
      targetUsers.push(...platformTargets)
    }

    // Also notify the unassigned user
    const userTargets = await this.notificationTargetService.getSpecificUserTarget(
      user.id,
      'ASSIGNED_USER'
    )
    targetUsers.push(...userTargets)

    const event = new UserUnassignedEvent(
      user,
      unassignedBy,
      removedRole,
      activityCollectionId,
      removedCollectionId,
      targetUsers
    )
    user.addDomainEvent(event)
  }
}
