import { ServicesInitializer } from '../../../shared/services/servicesInitializer'
import { activitiesServices } from '../../activities/services'
import { JWTAuthorizationService } from './jwtAuthorizationService'
import { UserAssignmentEventService } from './UserAssignmentEventService'

type UsersServices = {
  authorization: JWTAuthorizationService
  userAssignmentEvent: UserAssignmentEventService
}

class UsersServicesInitializer extends ServicesInitializer<UsersServices> {
  protected async setup(): Promise<UsersServices> {
    const notificationTargetService =
      activitiesServices.get('notificationTarget')

    return {
      authorization: new JWTAuthorizationService(),
      userAssignmentEvent: new UserAssignmentEventService(
        notificationTargetService,
      ),
    }
  }
}

const usersServices = new UsersServicesInitializer()

export { usersServices }
