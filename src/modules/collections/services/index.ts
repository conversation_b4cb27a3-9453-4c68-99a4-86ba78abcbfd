import { ServicesInitializer } from '../../../shared/services/servicesInitializer'
import { activitiesServices } from '../../activities/services'
import { CollectionEventService } from './CollectionEventService'

type CollectionsServices = {
  collectionEvent: CollectionEventService
}

class CollectionsServicesInitializer extends ServicesInitializer<CollectionsServices> {
  protected async setup(): Promise<CollectionsServices> {
    const notificationTargetService =
      activitiesServices.get('notificationTarget')

    return {
      collectionEvent: new CollectionEventService(notificationTargetService),
    }
  }
}

const collectionsServices = new CollectionsServicesInitializer()

export { collectionsServices }
