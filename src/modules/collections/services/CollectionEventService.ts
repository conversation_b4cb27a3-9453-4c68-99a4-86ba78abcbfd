import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { NotificationTargetService } from '../../activities/services/NotificationTargetService'
import { Collection, CollectionProps } from '../domain/collection'
import {
  CollectionCreatedEvent,
  CollectionUpdatedEvent,
} from '../domain/events'

/**
 * Service to handle collection-related domain events
 */
export class CollectionEventService {
  constructor(
    private readonly notificationTargetService: NotificationTargetService,
  ) {}

  /**
   * Trigger CollectionCreatedEvent with appropriate notification targets
   * Notifies platform managers (excluding the creator)
   */
  async triggerCollectionCreated(
    collection: Collection,
    createdBy: UniqueEntityID,
  ): Promise<void> {
    const targetUsers =
      await this.notificationTargetService.getPlatformManagerTargets(
        createdBy, // Exclude the creator from notifications
      )

    const event = new CollectionCreatedEvent(collection, createdBy, targetUsers)
    collection.addDomainEvent(event)
  }

  /**
   * Trigger CollectionUpdatedEvent with appropriate notification targets
   * Notifies collection members with notification roles (excluding the updater)
   */
  async triggerCollectionUpdated(
    collection: Collection,
    updatedBy: UniqueEntityID,
    updatedFields: (keyof CollectionProps)[],
  ): Promise<void> {
    const targetUsers =
      await this.notificationTargetService.getCollectionNotificationTargets(
        collection.id,
        updatedBy, // Exclude the updater from notifications
      )

    const event = new CollectionUpdatedEvent(
      collection,
      updatedBy,
      updatedFields,
      targetUsers,
    )
    collection.addDomainEvent(event)
  }
}
