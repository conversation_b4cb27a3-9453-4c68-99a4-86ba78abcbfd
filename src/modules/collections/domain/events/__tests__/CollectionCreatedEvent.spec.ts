import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotificationTarget } from '../../../../activities/domain/events/interfaces/IActivityDomainEvent'
import { stubCollection } from '../../__stubs__/collection.stub'
import { CollectionCreatedEvent } from '../CollectionCreatedEvent'

describe('CollectionCreatedEvent', () => {
  it('should create a CollectionCreatedEvent with correct properties', () => {
    const collection = stubCollection()
    const createdBy = new UniqueEntityID()
    const targetUsers: NotificationTarget[] = [
      {
        userId: new UniqueEntityID(),
        reason: 'PLATFORM_MANAGER'
      }
    ]

    const event = new CollectionCreatedEvent(collection, createdBy, targetUsers)

    expect(event.eventName).toBe(CollectionCreatedEvent.eventIdentifier)
    expect(event.activityType).toBe('COLLECTION_CREATE')
    expect(event.collectionId).toBe(collection.id)
    expect(event.targetUsers).toEqual(targetUsers)
    expect(event.collection).toBe(collection)
    expect(event.createdBy).toBe(createdBy)
    expect(event.getEntityId()).toBe(collection.id)
  })

  it('should include correct activity data', () => {
    const collection = stubCollection()
    const createdBy = new UniqueEntityID()
    const targetUsers: NotificationTarget[] = []

    const event = new CollectionCreatedEvent(collection, createdBy, targetUsers)

    expect(event.activityData).toEqual({
      collectionId: collection.id.value,
      name: collection.name,
      description: collection.description,
      createdBy: createdBy.value,
      hasServices: !!collection.services,
      hasFieldGroups: !!collection.fieldGroups
    })
  })

  it('should have correct event identifier', () => {
    expect(CollectionCreatedEvent.eventIdentifier).toBe('collection:created')
  })
})
