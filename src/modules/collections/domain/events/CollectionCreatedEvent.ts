import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { BaseActivityDomainEvent } from '../../../activities/domain/events/BaseActivityDomainEvent'
import { NotificationTarget } from '../../../activities/domain/events/interfaces/IActivityDomainEvent'
import { Collection } from '../collection'

export class CollectionCreatedEvent extends BaseActivityDomainEvent {
  public static readonly eventIdentifier: string = 'collection:created'
  public readonly collection: Collection
  public readonly createdBy: UniqueEntityID

  constructor(
    collection: Collection,
    createdBy: UniqueEntityID,
    targetUsers: NotificationTarget[],
  ) {
    const activityData = {
      collectionId: collection.id.value,
      name: collection.name,
      description: collection.description,
      createdBy: createdBy.value,
      hasServices: !!collection.services,
      hasFieldGroups: !!collection.fieldGroups,
    }

    super(
      CollectionCreatedEvent.eventIdentifier,
      'COLLECTION_CREATE',
      activityData,
      collection.id,
      targetUsers,
    )

    this.collection = collection
    this.createdBy = createdBy
  }

  public getEntityId(): UniqueEntityID {
    return this.collection.id
  }
}
