import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { BaseActivityDomainEvent } from '../../../activities/domain/events/BaseActivityDomainEvent'
import { NotificationTarget } from '../../../activities/domain/events/interfaces/IActivityDomainEvent'
import { Collection, CollectionProps } from '../collection'

export class CollectionUpdatedEvent extends BaseActivityDomainEvent {
  public static readonly eventIdentifier: string = 'collection:updated'
  public readonly collection: Collection
  public readonly updatedBy: UniqueEntityID
  public readonly updatedFields: (keyof CollectionProps)[]

  constructor(
    collection: Collection,
    updatedBy: UniqueEntityID,
    updatedFields: (keyof CollectionProps)[],
    targetUsers: NotificationTarget[],
  ) {
    const activityData = {
      collectionId: collection.id.value,
      name: collection.name,
      description: collection.description,
      updatedBy: updatedBy.value,
      updatedFields,
      hasServices: !!collection.services,
      hasFieldGroups: !!collection.fieldGroups,
    }

    super(
      CollectionUpdatedEvent.eventIdentifier,
      'COLLECTION_UPDATE',
      activityData,
      collection.id,
      targetUsers,
    )

    this.collection = collection
    this.updatedBy = updatedBy
    this.updatedFields = updatedFields
  }

  public getEntityId(): UniqueEntityID {
    return this.collection.id
  }
}
