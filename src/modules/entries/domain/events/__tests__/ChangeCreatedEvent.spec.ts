import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotificationTarget } from '../../../../activities/domain/events/interfaces/IActivityDomainEvent'
import { stubChange } from '../../__stubs__/change.stub'
import { ChangeCreatedEvent } from '../ChangeCreatedEvent'

describe('ChangeCreatedEvent', () => {
  it('should create a ChangeCreatedEvent with correct properties', () => {
    const change = stubChange()
    const collectionId = new UniqueEntityID()
    const targetUsers: NotificationTarget[] = [
      {
        userId: new UniqueEntityID(),
        reason: 'COLLECTION_MANAGER',
      },
      {
        userId: new UniqueEntityID(),
        reason: 'EDITOR',
      },
    ]

    const event = new ChangeCreatedEvent(change, collectionId, targetUsers)

    expect(event.eventName).toBe(ChangeCreatedEvent.eventIdentifier)
    expect(event.activityType).toBe('CHANGE_CREATE')
    expect(event.collectionId).toBe(collectionId)
    expect(event.targetUsers).toEqual(targetUsers)
    expect(event.change).toBe(change)
    expect(event.getEntityId()).toBe(change.id)
  })

  it('should include correct activity data', () => {
    const change = stubChange()
    const collectionId = new UniqueEntityID()
    const targetUsers: NotificationTarget[] = []

    const event = new ChangeCreatedEvent(change, collectionId, targetUsers)

    expect(event.activityData).toEqual({
      changeId: change.id.value,
      entryId: change.entryId.value,
      createdBy: change.createdBy.value,
      status: change.status,
      dataFieldCount: change.data.length,
    })
  })

  it('should have correct event identifier', () => {
    expect(ChangeCreatedEvent.eventIdentifier).toBe('change:created')
  })
})
