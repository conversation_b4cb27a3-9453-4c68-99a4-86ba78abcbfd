import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { NotificationTarget } from '../../../../activities/domain/events/interfaces/IActivityDomainEvent'
import { stubEntry } from '../../__stubs__/entry.stub'
import { EntryCreatedEvent } from '../EntryCreatedEvent'

describe('EntryCreatedEvent', () => {
  it('should create an EntryCreatedEvent with correct properties', () => {
    const entry = stubEntry()
    const targetUsers: NotificationTarget[] = [
      {
        userId: new UniqueEntityID(),
        reason: 'COLLECTION_MANAGER',
      },
      {
        userId: new UniqueEntityID(),
        reason: 'EDITOR',
      },
    ]

    const event = new EntryCreatedEvent(entry, targetUsers)

    expect(event.eventName).toBe(EntryCreatedEvent.eventIdentifier)
    expect(event.activityType).toBe('ENTRY_CREATE')
    expect(event.collectionId).toBe(entry.collectionId)
    expect(event.targetUsers).toEqual(targetUsers)
    expect(event.entry).toBe(entry)
    expect(event.getEntityId()).toBe(entry.id)
  })

  it('should include correct activity data', () => {
    const entry = stubEntry()
    const targetUsers: NotificationTarget[] = []

    const event = new EntryCreatedEvent(entry, targetUsers)

    expect(event.activityData).toEqual({
      entryId: entry.id.value,
      visibility: entry.visibility,
      createdBy: entry.createdBy.value,
      dataFieldCount: entry.data.length,
    })
  })

  it('should have correct event identifier', () => {
    expect(EntryCreatedEvent.eventIdentifier).toBe('entry:created')
  })
})
