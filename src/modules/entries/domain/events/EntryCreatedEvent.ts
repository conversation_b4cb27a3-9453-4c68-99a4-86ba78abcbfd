import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { BaseActivityDomainEvent } from '../../../activities/domain/events/BaseActivityDomainEvent'
import { NotificationTarget } from '../../../activities/domain/events/interfaces/IActivityDomainEvent'
import { Entry } from '../entry'

export class EntryCreatedEvent extends BaseActivityDomainEvent {
  public static readonly eventIdentifier: string = 'entry:created'
  public readonly entry: Entry

  constructor(entry: Entry, targetUsers: NotificationTarget[]) {
    const activityData = {
      entryId: entry.id.value,
      visibility: entry.visibility,
      createdBy: entry.createdBy.value,
      dataFieldCount: entry.data.length,
    }

    super(
      EntryCreatedEvent.eventIdentifier,
      'ENTRY_CREATE',
      activityData,
      entry.collectionId,
      targetUsers,
    )

    this.entry = entry
  }

  public getEntityId(): UniqueEntityID {
    return this.entry.id
  }
}
