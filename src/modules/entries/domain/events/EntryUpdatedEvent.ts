import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { BaseActivityDomainEvent } from '../../../activities/domain/events/BaseActivityDomainEvent'
import { NotificationTarget } from '../../../activities/domain/events/interfaces/IActivityDomainEvent'
import { Entry, EntryProps } from '../entry'

export class EntryUpdatedEvent extends BaseActivityDomainEvent {
  public static readonly eventIdentifier: string = 'entry:updated'
  public readonly entry: Entry
  public readonly updatedBy: UniqueEntityID
  public readonly updatedFields: (keyof EntryProps)[]

  constructor(
    entry: Entry,
    updatedBy: UniqueEntityID,
    updatedFields: (keyof EntryProps)[],
    targetUsers: NotificationTarget[],
  ) {
    const activityData = {
      entryId: entry.id.value,
      updatedBy: updatedBy.value,
      updatedFields,
      visibility: entry.visibility,
      dataFieldCount: entry.data.length,
    }

    super(
      EntryUpdatedEvent.eventIdentifier,
      'ENTRY_UPDATE',
      activityData,
      entry.collectionId,
      targetUsers,
    )

    this.entry = entry
    this.updatedBy = updatedBy
    this.updatedFields = updatedFields
  }

  public getEntityId(): UniqueEntityID {
    return this.entry.id
  }
}
