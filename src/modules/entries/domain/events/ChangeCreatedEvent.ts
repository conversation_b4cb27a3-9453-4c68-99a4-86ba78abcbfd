import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { BaseActivityDomainEvent } from '../../../activities/domain/events/BaseActivityDomainEvent'
import { NotificationTarget } from '../../../activities/domain/events/interfaces/IActivityDomainEvent'
import { Change } from '../change'

export class ChangeCreatedEvent extends BaseActivityDomainEvent {
  public static readonly eventIdentifier: string = 'change:created'
  public readonly change: Change
  public readonly collectionIdForChange: UniqueEntityID

  constructor(
    change: Change,
    collectionId: UniqueEntityID,
    targetUsers: NotificationTarget[],
  ) {
    const activityData = {
      changeId: change.id.value,
      entryId: change.entryId.value,
      createdBy: change.createdBy.value,
      status: change.status,
      dataFieldCount: change.data.length,
    }

    super(
      ChangeCreatedEvent.eventIdentifier,
      'CHANGE_CREATE',
      activityData,
      collectionId,
      targetUsers,
    )

    this.change = change
    this.collectionIdForChange = collectionId
  }

  public getEntityId(): UniqueEntityID {
    return this.change.id
  }
}
