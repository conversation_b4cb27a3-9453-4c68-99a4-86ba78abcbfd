import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { BaseActivityDomainEvent } from '../../../activities/domain/events/BaseActivityDomainEvent'
import { NotificationTarget } from '../../../activities/domain/events/interfaces/IActivityDomainEvent'
import { Entry } from '../entry'

export class EntryDeletedEvent extends BaseActivityDomainEvent {
  public static readonly eventIdentifier: string = 'entry:deleted'
  public readonly entry: Entry
  public readonly deletedBy: UniqueEntityID

  constructor(
    entry: Entry,
    deletedBy: UniqueEntityID,
    targetUsers: NotificationTarget[],
  ) {
    const activityData = {
      entryId: entry.id.value,
      deletedBy: deletedBy.value,
      visibility: entry.visibility,
      originalCreatedBy: entry.createdBy.value,
      dataFieldCount: entry.data.length,
    }

    super(
      EntryDeletedEvent.eventIdentifier,
      'ENTRY_DELETE',
      activityData,
      entry.collectionId,
      targetUsers,
    )

    this.entry = entry
    this.deletedBy = deletedBy
  }

  public getEntityId(): UniqueEntityID {
    return this.entry.id
  }
}
