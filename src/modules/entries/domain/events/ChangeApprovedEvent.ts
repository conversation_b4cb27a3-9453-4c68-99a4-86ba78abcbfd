import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { BaseActivityDomainEvent } from '../../../activities/domain/events/BaseActivityDomainEvent'
import { NotificationTarget } from '../../../activities/domain/events/interfaces/IActivityDomainEvent'
import { Change } from '../change'

export class ChangeApprovedEvent extends BaseActivityDomainEvent {
  public static readonly eventIdentifier: string = 'change:approved'
  public readonly change: Change
  public readonly collectionIdForChange: UniqueEntityID

  constructor(
    change: Change,
    collectionId: UniqueEntityID,
    targetUsers: NotificationTarget[],
  ) {
    const activityData = {
      changeId: change.id.value,
      entryId: change.entryId.value,
      createdBy: change.createdBy.value,
      reviewedBy: change.reviewedBy?.value,
      status: change.status,
      reviewerMessage: change.reviewerMessage,
      dataFieldCount: change.data.length,
    }

    super(
      ChangeApprovedEvent.eventIdentifier,
      'CHANGE_APPROVE',
      activityData,
      collectionId,
      targetUsers,
    )

    this.change = change
    this.collectionIdForChange = collectionId
  }

  public getEntityId(): UniqueEntityID {
    return this.change.id
  }
}
