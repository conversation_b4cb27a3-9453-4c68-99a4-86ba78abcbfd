import { ServicesInitializer } from '../../../shared/services/servicesInitializer'
import { activitiesServices } from '../../activities/services'
import { entryRepository } from '../repositories'
import { ChangeEventService } from './ChangeEventService'
import { EntryEventService } from './EntryEventService'

type EntriesServices = {
  entryEvent: EntryEventService
  changeEvent: ChangeEventService
}

class EntriesServicesInitializer extends ServicesInitializer<EntriesServices> {
  protected async setup(): Promise<EntriesServices> {
    const notificationTargetService =
      activitiesServices.get('notificationTarget')

    return {
      entryEvent: new EntryEventService(notificationTargetService),
      changeEvent: new ChangeEventService(
        notificationTargetService,
        entryRepository,
      ),
    }
  }
}

const entriesServices = new EntriesServicesInitializer()

export { entriesServices }
