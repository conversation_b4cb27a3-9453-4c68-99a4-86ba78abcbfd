import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { NotificationTargetService } from '../../../activities/services/NotificationTargetService'
import { stubChange } from '../../domain/__stubs__/change.stub'
import { stubEntry } from '../../domain/__stubs__/entry.stub'
import {
  ChangeCreatedEvent,
  ChangeApprovedEvent,
  ChangeRejectedEvent,
} from '../../domain/events'
import { GenericEntryRepository } from '../../repositories/interfaces/genericEntryRepository'
import { ChangeEventService } from '../ChangeEventService'

// Mock the dependencies
jest.mock('../../../activities/services/NotificationTargetService')

describe('ChangeEventService', () => {
  let changeEventService: ChangeEventService
  let mockNotificationTargetService: jest.Mocked<NotificationTargetService>
  let mockEntryRepository: jest.Mocked<GenericEntryRepository>

  beforeEach(() => {
    mockNotificationTargetService = new NotificationTargetService(
      {} as any,
    ) as jest.Mocked<NotificationTargetService>

    mockEntryRepository = {
      getById: jest.fn(),
    } as any

    changeEventService = new ChangeEventService(
      mockNotificationTargetService,
      mockEntryRepository,
    )
  })

  describe('triggerChangeCreated', () => {
    it('should add ChangeCreatedEvent to change with correct targets', async () => {
      const change = stubChange()
      const entry = stubEntry()
      const mockTargets = [
        { userId: new UniqueEntityID(), reason: 'COLLECTION_MANAGER' as const },
      ]

      mockEntryRepository.getById.mockResolvedValue(entry)
      mockNotificationTargetService.getCollectionNotificationTargets.mockResolvedValue(
        mockTargets,
      )

      const addDomainEventSpy = jest.spyOn(change, 'addDomainEvent')

      await changeEventService.triggerChangeCreated(change)

      expect(mockEntryRepository.getById).toHaveBeenCalledWith(change.entryId)
      expect(
        mockNotificationTargetService.getCollectionNotificationTargets,
      ).toHaveBeenCalledWith(entry.collectionId, change.createdBy)
      expect(addDomainEventSpy).toHaveBeenCalledWith(
        expect.any(ChangeCreatedEvent),
      )

      const addedEvent = addDomainEventSpy.mock
        .calls[0][0] as ChangeCreatedEvent
      expect(addedEvent.change).toBe(change)
      expect(addedEvent.collectionIdForChange).toBe(entry.collectionId)
      expect(addedEvent.targetUsers).toEqual(mockTargets)
    })
  })

  describe('triggerChangeApproved', () => {
    it('should add ChangeApprovedEvent to change with correct targets', async () => {
      const change = stubChange()
      const entry = stubEntry()
      const mockTargets = [
        { userId: change.createdBy, reason: 'CHANGE_CREATOR' as const },
      ]

      mockEntryRepository.getById.mockResolvedValue(entry)
      mockNotificationTargetService.getSpecificUserTarget.mockResolvedValue(
        mockTargets,
      )

      const addDomainEventSpy = jest.spyOn(change, 'addDomainEvent')

      await changeEventService.triggerChangeApproved(change)

      expect(mockEntryRepository.getById).toHaveBeenCalledWith(change.entryId)
      expect(
        mockNotificationTargetService.getSpecificUserTarget,
      ).toHaveBeenCalledWith(change.createdBy, 'CHANGE_CREATOR')
      expect(addDomainEventSpy).toHaveBeenCalledWith(
        expect.any(ChangeApprovedEvent),
      )

      const addedEvent = addDomainEventSpy.mock
        .calls[0][0] as ChangeApprovedEvent
      expect(addedEvent.change).toBe(change)
      expect(addedEvent.collectionIdForChange).toBe(entry.collectionId)
      expect(addedEvent.targetUsers).toEqual(mockTargets)
    })
  })

  describe('triggerChangeRejected', () => {
    it('should add ChangeRejectedEvent to change with correct targets', async () => {
      const change = stubChange()
      const entry = stubEntry()
      const mockTargets = [
        { userId: change.createdBy, reason: 'CHANGE_CREATOR' as const },
      ]

      mockEntryRepository.getById.mockResolvedValue(entry)
      mockNotificationTargetService.getSpecificUserTarget.mockResolvedValue(
        mockTargets,
      )

      const addDomainEventSpy = jest.spyOn(change, 'addDomainEvent')

      await changeEventService.triggerChangeRejected(change)

      expect(mockEntryRepository.getById).toHaveBeenCalledWith(change.entryId)
      expect(
        mockNotificationTargetService.getSpecificUserTarget,
      ).toHaveBeenCalledWith(change.createdBy, 'CHANGE_CREATOR')
      expect(addDomainEventSpy).toHaveBeenCalledWith(
        expect.any(ChangeRejectedEvent),
      )

      const addedEvent = addDomainEventSpy.mock
        .calls[0][0] as ChangeRejectedEvent
      expect(addedEvent.change).toBe(change)
      expect(addedEvent.collectionIdForChange).toBe(entry.collectionId)
      expect(addedEvent.targetUsers).toEqual(mockTargets)
    })
  })
})
