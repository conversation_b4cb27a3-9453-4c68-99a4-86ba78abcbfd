import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { NotificationTargetService } from '../../../activities/services/NotificationTargetService'
import { stubEntry } from '../../domain/__stubs__/entry.stub'
import {
  EntryCreatedEvent,
  EntryUpdatedEvent,
  EntryDeletedEvent,
} from '../../domain/events'
import { EntryEventService } from '../EntryEventService'

// Mock the NotificationTargetService
jest.mock('../../../activities/services/NotificationTargetService')

describe('EntryEventService', () => {
  let entryEventService: EntryEventService
  let mockNotificationTargetService: jest.Mocked<NotificationTargetService>

  beforeEach(() => {
    mockNotificationTargetService = new NotificationTargetService(
      {} as any,
    ) as jest.Mocked<NotificationTargetService>
    entryEventService = new EntryEventService(mockNotificationTargetService)
  })

  describe('triggerEntryCreated', () => {
    it('should add EntryCreatedEvent to entry with correct targets', async () => {
      const entry = stubEntry()
      const mockTargets = [
        { userId: new UniqueEntityID(), reason: 'COLLECTION_MANAGER' as const },
      ]

      mockNotificationTargetService.getCollectionNotificationTargets.mockResolvedValue(
        mockTargets,
      )

      const addDomainEventSpy = jest.spyOn(entry, 'addDomainEvent')

      await entryEventService.triggerEntryCreated(entry)

      expect(
        mockNotificationTargetService.getCollectionNotificationTargets,
      ).toHaveBeenCalledWith(entry.collectionId, entry.createdBy)
      expect(addDomainEventSpy).toHaveBeenCalledWith(
        expect.any(EntryCreatedEvent),
      )

      const addedEvent = addDomainEventSpy.mock.calls[0][0] as EntryCreatedEvent
      expect(addedEvent.entry).toBe(entry)
      expect(addedEvent.targetUsers).toEqual(mockTargets)
    })
  })

  describe('triggerEntryUpdated', () => {
    it('should add EntryUpdatedEvent to entry with correct targets', async () => {
      const entry = stubEntry()
      const updatedBy = new UniqueEntityID()
      const updatedFields = ['visibility'] as const
      const mockTargets = [
        { userId: new UniqueEntityID(), reason: 'EDITOR' as const },
      ]

      mockNotificationTargetService.getCollectionNotificationTargets.mockResolvedValue(
        mockTargets,
      )

      const addDomainEventSpy = jest.spyOn(entry, 'addDomainEvent')

      await entryEventService.triggerEntryUpdated(
        entry,
        updatedBy,
        updatedFields,
      )

      expect(
        mockNotificationTargetService.getCollectionNotificationTargets,
      ).toHaveBeenCalledWith(entry.collectionId, updatedBy)
      expect(addDomainEventSpy).toHaveBeenCalledWith(
        expect.any(EntryUpdatedEvent),
      )

      const addedEvent = addDomainEventSpy.mock.calls[0][0] as EntryUpdatedEvent
      expect(addedEvent.entry).toBe(entry)
      expect(addedEvent.updatedBy).toBe(updatedBy)
      expect(addedEvent.updatedFields).toEqual(updatedFields)
      expect(addedEvent.targetUsers).toEqual(mockTargets)
    })
  })

  describe('triggerEntryDeleted', () => {
    it('should add EntryDeletedEvent to entry with correct targets', async () => {
      const entry = stubEntry()
      const deletedBy = new UniqueEntityID()
      const mockTargets = [
        { userId: new UniqueEntityID(), reason: 'COLLECTION_MANAGER' as const },
      ]

      mockNotificationTargetService.getCollectionNotificationTargets.mockResolvedValue(
        mockTargets,
      )

      const addDomainEventSpy = jest.spyOn(entry, 'addDomainEvent')

      await entryEventService.triggerEntryDeleted(entry, deletedBy)

      expect(
        mockNotificationTargetService.getCollectionNotificationTargets,
      ).toHaveBeenCalledWith(entry.collectionId, deletedBy)
      expect(addDomainEventSpy).toHaveBeenCalledWith(
        expect.any(EntryDeletedEvent),
      )

      const addedEvent = addDomainEventSpy.mock.calls[0][0] as EntryDeletedEvent
      expect(addedEvent.entry).toBe(entry)
      expect(addedEvent.deletedBy).toBe(deletedBy)
      expect(addedEvent.targetUsers).toEqual(mockTargets)
    })
  })
})
