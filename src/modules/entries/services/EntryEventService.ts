import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { NotificationTargetService } from '../../activities/services/NotificationTargetService'
import { Entry, EntryProps } from '../domain/entry'
import {
  EntryCreatedEvent,
  EntryUpdatedEvent,
  EntryDeletedEvent,
} from '../domain/events'

/**
 * Service to handle entry-related domain events
 */
export class EntryEventService {
  constructor(
    private readonly notificationTargetService: NotificationTargetService,
  ) {}

  /**
   * Trigger EntryCreatedEvent with appropriate notification targets
   */
  async triggerEntryCreated(entry: Entry): Promise<void> {
    const targetUsers =
      await this.notificationTargetService.getCollectionNotificationTargets(
        entry.collectionId,
        entry.createdBy, // Exclude the creator from notifications
      )

    const event = new EntryCreatedEvent(entry, targetUsers)
    entry.addDomainEvent(event)
  }

  /**
   * Trigger EntryUpdatedEvent with appropriate notification targets
   */
  async triggerEntryUpdated(
    entry: Entry,
    updatedBy: UniqueEntityID,
    updatedFields: (keyof EntryProps)[],
  ): Promise<void> {
    const targetUsers =
      await this.notificationTargetService.getCollectionNotificationTargets(
        entry.collectionId,
        updatedBy, // Exclude the updater from notifications
      )

    const event = new EntryUpdatedEvent(
      entry,
      updatedBy,
      updatedFields,
      targetUsers,
    )
    entry.addDomainEvent(event)
  }

  /**
   * Trigger EntryDeletedEvent with appropriate notification targets
   */
  async triggerEntryDeleted(
    entry: Entry,
    deletedBy: UniqueEntityID,
  ): Promise<void> {
    const targetUsers =
      await this.notificationTargetService.getCollectionNotificationTargets(
        entry.collectionId,
        deletedBy, // Exclude the deleter from notifications
      )

    const event = new EntryDeletedEvent(entry, deletedBy, targetUsers)
    entry.addDomainEvent(event)
  }
}
