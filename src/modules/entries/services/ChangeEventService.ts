import { NotificationTargetService } from '../../activities/services/NotificationTargetService'
import { Change } from '../domain/change'
import {
  ChangeCreatedEvent,
  ChangeApprovedEvent,
  ChangeRejectedEvent,
} from '../domain/events'
import { GenericEntryRepository } from '../repositories/interfaces/genericEntryRepository'

/**
 * Service to handle change-related domain events
 */
export class ChangeEventService {
  constructor(
    private readonly notificationTargetService: NotificationTargetService,
    private readonly entryRepository: GenericEntryRepository,
  ) {}

  /**
   * Trigger ChangeCreatedEvent with appropriate notification targets
   * Notifies collection managers and editors (excluding the change creator)
   */
  async triggerChangeCreated(change: Change): Promise<void> {
    const entry = await this.entryRepository.getById(change.entryId)
    const collectionId = entry.collectionId

    const targetUsers =
      await this.notificationTargetService.getCollectionNotificationTargets(
        collectionId,
        change.createdBy, // Exclude the change creator from notifications
      )

    const event = new ChangeCreatedEvent(change, collectionId, targetUsers)
    change.addDomainEvent(event)
  }

  /**
   * Trigger ChangeApprovedEvent with appropriate notification targets
   * Notifies the original change creator
   */
  async triggerChangeApproved(change: Change): Promise<void> {
    const entry = await this.entryRepository.getById(change.entryId)
    const collectionId = entry.collectionId

    const targetUsers =
      await this.notificationTargetService.getSpecificUserTarget(
        change.createdBy,
        'CHANGE_CREATOR',
      )

    const event = new ChangeApprovedEvent(change, collectionId, targetUsers)
    change.addDomainEvent(event)
  }

  /**
   * Trigger ChangeRejectedEvent with appropriate notification targets
   * Notifies the original change creator
   */
  async triggerChangeRejected(change: Change): Promise<void> {
    const entry = await this.entryRepository.getById(change.entryId)
    const collectionId = entry.collectionId

    const targetUsers =
      await this.notificationTargetService.getSpecificUserTarget(
        change.createdBy,
        'CHANGE_CREATOR',
      )

    const event = new ChangeRejectedEvent(change, collectionId, targetUsers)
    change.addDomainEvent(event)
  }
}
