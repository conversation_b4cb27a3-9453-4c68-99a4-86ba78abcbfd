import { ServicesInitializer } from '../../../shared/services/servicesInitializer'
import { userRepository } from '../../users/repositories'
import { NotificationTargetService } from './NotificationTargetService'

type ActivitiesServices = {
  notificationTarget: NotificationTargetService
}

class ActivitiesServicesInitializer extends ServicesInitializer<ActivitiesServices> {
  protected async setup(): Promise<ActivitiesServices> {
    return {
      notificationTarget: new NotificationTargetService(userRepository),
    }
  }
}

const activitiesServices = new ActivitiesServicesInitializer()

export { activitiesServices }
