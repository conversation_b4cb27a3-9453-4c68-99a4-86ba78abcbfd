import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { User } from '../../users/domain/user'
import { GenericUserRepository } from '../../users/repositories/interfaces/genericUserRepository'
import { Role } from '../../users/types/user'
import {
  NotificationTarget,
  NotificationReason,
} from '../domain/events/interfaces/IActivityDomainEvent'

/**
 * Service to determine notification targets for various business events
 */
export class NotificationTargetService {
  constructor(private readonly userRepository: GenericUserRepository) {}

  /**
   * Get users who should be notified for collection-level events
   * Includes collection managers and editors, excluding the actor if specified
   */
  async getCollectionNotificationTargets(
    collectionId: UniqueEntityID,
    excludeUserId?: UniqueEntityID,
  ): Promise<NotificationTarget[]> {
    const collectionUsers =
      await this.userRepository.getAllByCollectionId(collectionId)
    const targets: NotificationTarget[] = []

    for (const user of collectionUsers.items) {
      // Skip the user who performed the action
      if (excludeUserId && user.id.equals(excludeUserId)) {
        continue
      }

      // Skip users who don't have email notifications enabled
      if (!user.emailNotifications) {
        continue
      }

      const userRole = this.getUserRoleInCollection(user, collectionId)

      // Only notify collection managers and editors
      if (userRole === 'COLLECTION_MANAGER') {
        targets.push({
          userId: user.id,
          reason: 'COLLECTION_MANAGER',
        })
      } else if (userRole === 'EDITOR') {
        targets.push({
          userId: user.id,
          reason: 'EDITOR',
        })
      }
    }

    return targets
  }

  /**
   * Get platform managers who should be notified for platform-level events
   */
  async getPlatformManagerTargets(
    excludeUserId?: UniqueEntityID,
  ): Promise<NotificationTarget[]> {
    const allUsers = await this.userRepository.getAll()
    const targets: NotificationTarget[] = []

    for (const user of allUsers.items) {
      // Skip the user who performed the action
      if (excludeUserId && user.id.equals(excludeUserId)) {
        continue
      }

      // Skip users who don't have email notifications enabled
      if (!user.emailNotifications) {
        continue
      }

      // Check if user has platform manager role
      const hasPlatformRole = user.userAssignments.some(
        (assignment) => assignment.role === 'PLATFORM_MANAGER',
      )

      if (hasPlatformRole) {
        targets.push({
          userId: user.id,
          reason: 'PLATFORM_MANAGER',
        })
      }
    }

    return targets
  }

  /**
   * Get specific user as notification target
   */
  async getSpecificUserTarget(
    userId: UniqueEntityID,
    reason: NotificationReason,
  ): Promise<NotificationTarget[]> {
    try {
      const user = await this.userRepository.getById(userId)

      // Only notify if user has email notifications enabled
      if (!user.emailNotifications) {
        return []
      }

      return [
        {
          userId: user.id,
          reason,
        },
      ]
    } catch {
      // User not found or error - return empty array
      return []
    }
  }

  /**
   * Helper method to get user's role in a specific collection
   */
  private getUserRoleInCollection(
    user: User,
    collectionId: UniqueEntityID,
  ): Role | null {
    const assignment = user.userAssignments.find((assignment) =>
      assignment.collectionId?.equals(collectionId),
    )
    return assignment?.role || null
  }
}
