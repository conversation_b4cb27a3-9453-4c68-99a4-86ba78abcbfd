import { stubNotification } from '../../__stubs__/notification.stub'
import { NotificationCreatedEvent } from '../NotificationCreatedEvent'

describe('NotificationCreatedEvent', () => {
  it('should create a NotificationCreatedEvent with correct properties', () => {
    const notification = stubNotification()

    const event = new NotificationCreatedEvent(notification)

    expect(event.eventName).toBe(NotificationCreatedEvent.eventIdentifier)
    expect(event.notification).toBe(notification)
    expect(event.getEntityId()).toBe(notification.id)
    expect(event.dateTimeOccurred).toBeInstanceOf(Date)
  })

  it('should have correct event identifier', () => {
    expect(NotificationCreatedEvent.eventIdentifier).toBe('notification:created')
  })
})
