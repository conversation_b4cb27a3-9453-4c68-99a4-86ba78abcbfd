import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { Notification } from '../notification'

export class NotificationCreatedEvent extends DomainEvent {
  public static readonly eventIdentifier: string = 'notification:created'
  public readonly notification: Notification

  constructor(notification: Notification) {
    super(NotificationCreatedEvent.eventIdentifier)
    this.notification = notification
  }

  public getEntityId(): UniqueEntityID {
    return this.notification.id
  }
}
