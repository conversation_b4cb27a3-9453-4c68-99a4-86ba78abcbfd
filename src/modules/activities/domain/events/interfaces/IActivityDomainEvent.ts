import { IDomainEvent } from '../../../../../shared/domain/events/interfaces/IDomainEvent'
import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'

/**
 * Represents the reason why a user should be notified
 */
export type NotificationReason =
  | 'COLLECTION_MANAGER'
  | 'EDITOR'
  | 'ENTRY_CREATOR'
  | 'CHANGE_CREATOR'
  | 'ASSIGNED_USER'
  | 'PLATFORM_MANAGER'

/**
 * Represents a target user for notifications
 */
export interface NotificationTarget {
  userId: UniqueEntityID
  reason: NotificationReason
}

/**
 * Base interface for domain events that trigger activity creation and notifications
 */
export interface IActivityDomainEvent extends IDomainEvent {
  readonly activityType: string
  readonly activityData: unknown
  readonly collectionId: UniqueEntityID
  readonly targetUsers: NotificationTarget[]
}
