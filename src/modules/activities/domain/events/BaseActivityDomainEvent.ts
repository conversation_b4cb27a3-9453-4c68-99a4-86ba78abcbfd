import { DomainEvent } from '../../../../shared/domain/events/DomainEvent'
import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import {
  IActivityDomainEvent,
  NotificationTarget,
} from './interfaces/IActivityDomainEvent'

/**
 * Base class for activity domain events that provides common functionality
 */
export abstract class BaseActivityDomainEvent
  extends DomainEvent
  implements IActivityDomainEvent
{
  public readonly activityType: string
  public readonly activityData: unknown
  public readonly collectionId: UniqueEntityID
  public readonly targetUsers: NotificationTarget[]

  constructor(
    eventName: string,
    activityType: string,
    activityData: unknown,
    collectionId: UniqueEntityID,
    targetUsers: NotificationTarget[],
  ) {
    super(eventName)
    this.activityType = activityType
    this.activityData = activityData
    this.collectionId = collectionId
    this.targetUsers = targetUsers
  }
}
