import { Entity } from '../../../shared/domain/entity'
import { UniqueEntityID } from '../../../shared/domain/uniqueEntityID'
import { NotificationAlreadySent } from '../errors/notificationAlreadySent'
import { NotificationCreatedEvent } from './events/NotificationCreatedEvent'

type NotificationProps = {
  userId: UniqueEntityID
  activityId: UniqueEntityID
  read: boolean
  sent: boolean
}

class Notification extends Entity<NotificationProps> {
  constructor(
    props: NotificationProps,
    id?: UniqueEntityID,
    createdAt?: Date,
    updatedAt?: Date,
    deleted?: boolean,
  ) {
    super(id, props, createdAt, updatedAt, deleted)
  }

  get userId() {
    return this.getProp('userId')
  }

  get activityId() {
    return this.getProp('activityId')
  }

  get read() {
    return this.getProp('read')
  }

  get sent() {
    return this.getProp('sent')
  }

  public notify() {
    if (this.sent) {
      throw new NotificationAlreadySent()
    }

    // Trigger domain event for email sending
    this.addDomainEvent(new NotificationCreatedEvent(this))
  }

  public markAsRead() {
    this.setProp('read', true)
  }
}

export { Notification, type NotificationProps }
