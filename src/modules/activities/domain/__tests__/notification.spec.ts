import {
  baseNotificationStubProps,
  stubNotification,
} from '../__stubs__/notification.stub'
import { NotificationCreatedEvent } from '../events/NotificationCreatedEvent'

describe('Notification', () => {
  it('should create a Notification entity', () => {
    const notification = stubNotification()

    expect(notification.id).toBeDefined()
    expect(notification.userId).toBe(baseNotificationStubProps.userId)
    expect(notification.activityId).toBe(baseNotificationStubProps.activityId)
    expect(notification.read).toBe(baseNotificationStubProps.read)
    expect(notification.sent).toBe(baseNotificationStubProps.sent)
  })

  it('should throw if notification already sent', () => {
    const notification = stubNotification({ sent: true })

    expect(() => notification.notify()).toThrow()
  })

  it('should add NotificationCreatedEvent when notify is called', () => {
    const notification = stubNotification({ sent: false })
    const addDomainEventSpy = jest.spyOn(notification, 'addDomainEvent')

    notification.notify()

    expect(addDomainEventSpy).toHaveBeenCalledWith(
      expect.any(NotificationCreatedEvent),
    )

    const addedEvent = addDomainEventSpy.mock
      .calls[0][0] as NotificationCreatedEvent
    expect(addedEvent.notification).toBe(notification)
  })
})
