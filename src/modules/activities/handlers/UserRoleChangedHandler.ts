import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { UserRoleChangedEvent } from '../../users/domain/events/UserRoleChangedEvent'
import { createActivityWithNotificationsUseCase } from '../useCases/createActivity'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivity/createActivityWithNotifications.dto'

export class UserRoleChangedHandler extends BaseDomainEventHandler<
  UserRoleChangedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(
      createActivityWithNotificationsUseCase,
      UserRoleChangedEvent,
      UserRoleChangedEvent.eventIdentifier
    )
  }

  protected parseInput(event: UserRoleChangedEvent): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
      actorUserId: event.changedBy.value
    }
  }
}
