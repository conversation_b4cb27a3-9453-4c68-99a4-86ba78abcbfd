import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { UserAssignedEvent } from '../../users/domain/events/UserAssignedEvent'
import { createActivityWithNotificationsUseCase } from '../useCases/createActivity'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivity/createActivityWithNotifications.dto'

export class UserAssignedHandler extends BaseDomainEventHandler<
  UserAssignedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(
      createActivityWithNotificationsUseCase,
      UserAssignedEvent,
      UserAssignedEvent.eventIdentifier
    )
  }

  protected parseInput(event: UserAssignedEvent): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
      actorUserId: event.assignedBy.value
    }
  }
}
