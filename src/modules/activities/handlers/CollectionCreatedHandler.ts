import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { CollectionCreatedEvent } from '../../collections/domain/events/CollectionCreatedEvent'
import { createActivityWithNotificationsUseCase } from '../useCases/createActivity'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivity/createActivityWithNotifications.dto'

export class CollectionCreatedHandler extends BaseDomainEventHandler<
  CollectionCreatedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(
      createActivityWithNotificationsUseCase,
      CollectionCreatedEvent,
      CollectionCreatedEvent.eventIdentifier
    )
  }

  protected parseInput(event: CollectionCreatedEvent): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
      actorUserId: event.createdBy.value
    }
  }
}
