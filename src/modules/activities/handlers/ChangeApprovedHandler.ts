import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { ChangeApprovedEvent } from '../../entries/domain/events/ChangeApprovedEvent'
import { createActivityWithNotificationsUseCase } from '../useCases/createActivity'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivity/createActivityWithNotifications.dto'

export class ChangeApprovedHandler extends BaseDomainEventHandler<
  ChangeApprovedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(
      createActivityWithNotificationsUseCase,
      ChangeApprovedEvent,
      ChangeApprovedEvent.eventIdentifier
    )
  }

  protected parseInput(event: ChangeApprovedEvent): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
      actorUserId: event.change.reviewedBy?.value || event.change.createdBy.value
    }
  }
}
