import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { ChangeCreatedEvent } from '../../entries/domain/events/ChangeCreatedEvent'
import { createActivityWithNotificationsUseCase } from '../useCases/createActivity'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivity/createActivityWithNotifications.dto'

export class ChangeCreatedHandler extends BaseDomainEventHandler<
  ChangeCreatedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(
      createActivityWithNotificationsUseCase,
      ChangeCreatedEvent,
      ChangeCreatedEvent.eventIdentifier
    )
  }

  protected parseInput(event: ChangeCreatedEvent): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
      actorUserId: event.change.createdBy.value
    }
  }
}
