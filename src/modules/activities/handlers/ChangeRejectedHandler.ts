import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { ChangeRejectedEvent } from '../../entries/domain/events/ChangeRejectedEvent'
import { createActivityWithNotificationsUseCase } from '../useCases/createActivity'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivity/createActivityWithNotifications.dto'

export class ChangeRejectedHandler extends BaseDomainEventHandler<
  ChangeRejectedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(
      createActivityWithNotificationsUseCase,
      ChangeRejectedEvent,
      ChangeRejectedEvent.eventIdentifier
    )
  }

  protected parseInput(event: ChangeRejectedEvent): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
      actorUserId: event.change.reviewedBy?.value || event.change.createdBy.value
    }
  }
}
