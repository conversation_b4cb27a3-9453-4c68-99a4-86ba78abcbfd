import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { EntryUpdatedEvent } from '../../entries/domain/events/EntryUpdatedEvent'
import { createActivityWithNotificationsUseCase } from '../useCases/createActivity'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivity/createActivityWithNotifications.dto'

export class EntryUpdatedHandler extends BaseDomainEventHandler<
  EntryUpdatedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(
      createActivityWithNotificationsUseCase,
      EntryUpdatedEvent,
      EntryUpdatedEvent.eventIdentifier
    )
  }

  protected parseInput(event: EntryUpdatedEvent): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
      actorUserId: event.updatedBy.value
    }
  }
}
