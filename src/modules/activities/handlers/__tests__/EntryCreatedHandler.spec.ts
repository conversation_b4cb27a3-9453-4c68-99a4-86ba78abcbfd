import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { stubEntry } from '../../../entries/domain/__stubs__/entry.stub'
import { EntryCreatedEvent } from '../../../entries/domain/events/EntryCreatedEvent'
import { NotificationTarget } from '../../domain/events/interfaces/IActivityDomainEvent'
import { createActivityWithNotificationsUseCase } from '../../useCases/createActivity'
import { EntryCreatedHandler } from '../EntryCreatedHandler'

// Mock the use case
jest.mock('../../useCases/createActivity', () => ({
  createActivityWithNotificationsUseCase: {
    execute: jest.fn(),
  },
}))

describe('EntryCreatedHandler', () => {
  let handler: EntryCreatedHandler
  let mockUseCase: jest.Mocked<typeof createActivityWithNotificationsUseCase>

  beforeEach(() => {
    mockUseCase = createActivityWithNotificationsUseCase as jest.Mocked<typeof createActivityWithNotificationsUseCase>
    handler = new EntryCreatedHandler()
  })

  it('should handle EntryCreatedEvent correctly', async () => {
    const entry = stubEntry()
    const targetUsers: NotificationTarget[] = [
      { userId: new UniqueEntityID(), reason: 'COLLECTION_MANAGER' }
    ]
    const event = new EntryCreatedEvent(entry, targetUsers)

    mockUseCase.execute.mockResolvedValue({} as any)

    await handler.handle(event)

    expect(mockUseCase.execute).toHaveBeenCalledWith({
      type: 'ENTRY_CREATE',
      data: event.activityData,
      collectionId: entry.collectionId.value,
      targetUsers,
      actorUserId: entry.createdBy.value
    })
  })

  it('should have correct event identifier', () => {
    expect(handler.eventIdentifier).toBe('entry:created')
  })
})
