import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { CollectionUpdatedEvent } from '../../collections/domain/events/CollectionUpdatedEvent'
import { createActivityWithNotificationsUseCase } from '../useCases/createActivity'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivity/createActivityWithNotifications.dto'

export class CollectionUpdatedHandler extends BaseDomainEventHandler<
  CollectionUpdatedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(
      createActivityWithNotificationsUseCase,
      CollectionUpdatedEvent,
      CollectionUpdatedEvent.eventIdentifier
    )
  }

  protected parseInput(event: CollectionUpdatedEvent): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
      actorUserId: event.updatedBy.value
    }
  }
}
