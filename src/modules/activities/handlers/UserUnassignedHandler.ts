import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { UserUnassignedEvent } from '../../users/domain/events/UserUnassignedEvent'
import { createActivityWithNotificationsUseCase } from '../useCases/createActivity'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivity/createActivityWithNotifications.dto'

export class UserUnassignedHandler extends BaseDomainEventHandler<
  UserUnassignedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(
      createActivityWithNotificationsUseCase,
      UserUnassignedEvent,
      UserUnassignedEvent.eventIdentifier
    )
  }

  protected parseInput(event: UserUnassignedEvent): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
      actorUserId: event.unassignedBy.value
    }
  }
}
