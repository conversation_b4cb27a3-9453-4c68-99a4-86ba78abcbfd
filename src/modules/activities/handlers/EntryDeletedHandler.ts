import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { EntryDeletedEvent } from '../../entries/domain/events/EntryDeletedEvent'
import { createActivityWithNotificationsUseCase } from '../useCases/createActivity'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivity/createActivityWithNotifications.dto'

export class EntryDeletedHandler extends BaseDomainEventHandler<
  EntryDeletedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(
      createActivityWithNotificationsUseCase,
      EntryDeletedEvent,
      EntryDeletedEvent.eventIdentifier
    )
  }

  protected parseInput(event: EntryDeletedEvent): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
      actorUserId: event.deletedBy.value
    }
  }
}
