import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { NotificationCreatedEvent } from '../domain/events/NotificationCreatedEvent'
import { sendEmailNotificationUseCase } from '../useCases/sendEmailNotification'
import { SendEmailNotificationDTO } from '../useCases/sendEmailNotification/sendEmailNotification.dto'

export class NotificationCreatedHandler extends BaseDomainEventHandler<
  NotificationCreatedEvent,
  SendEmailNotificationDTO
> {
  constructor() {
    super(
      sendEmailNotificationUseCase,
      NotificationCreatedEvent,
      NotificationCreatedEvent.eventIdentifier
    )
  }

  protected parseInput(event: NotificationCreatedEvent): SendEmailNotificationDTO {
    return {
      notificationId: event.notification.id.value
    }
  }
}
