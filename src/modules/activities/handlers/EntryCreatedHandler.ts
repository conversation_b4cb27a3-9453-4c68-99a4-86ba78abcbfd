import { BaseDomainEventHandler } from '../../../shared/domain/events/BaseDomainEventHandler'
import { EntryCreatedEvent } from '../../entries/domain/events/EntryCreatedEvent'
import { createActivityWithNotificationsUseCase } from '../useCases/createActivity'
import { CreateActivityWithNotificationsDTO } from '../useCases/createActivity/createActivityWithNotifications.dto'

export class EntryCreatedHandler extends BaseDomainEventHandler<
  EntryCreatedEvent,
  CreateActivityWithNotificationsDTO
> {
  constructor() {
    super(
      createActivityWithNotificationsUseCase,
      EntryCreatedEvent,
      EntryCreatedEvent.eventIdentifier
    )
  }

  protected parseInput(event: EntryCreatedEvent): CreateActivityWithNotificationsDTO {
    return {
      type: event.activityType,
      data: event.activityData,
      collectionId: event.collectionId.value,
      targetUsers: event.targetUsers,
      actorUserId: event.entry.createdBy.value
    }
  }
}
