import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { AuthContext } from '../../../../shared/infra/auth/authContext'
import { UseCase } from '../../../../shared/infra/useCase'
import { GenericCollectionRepository } from '../../../collections/repositories/interfaces/genericCollectionRepository'
import { GenericUserRepository } from '../../../users/repositories/interfaces/genericUserRepository'
import { Activity } from '../../domain/activity'
import { Notification } from '../../domain/notification'
import { ActivityDTO } from '../../dto/activityDTO'
import { ActivityMapper } from '../../mappers'
import { GenericActivityRepository } from '../../repositories/interfaces/genericActivityRepository'
import { GenericNotificationRepository } from '../../repositories/interfaces/genericNotificationRepository'
import { CreateActivityWithNotificationsDTO } from './createActivityWithNotifications.dto'

class CreateActivityWithNotifications extends UseCase<CreateActivityWithNotificationsDTO, ActivityDTO> {
  constructor(
    private readonly _activityRepository: GenericActivityRepository,
    private readonly _notificationRepository: GenericNotificationRepository,
    private readonly _collectionRepository: GenericCollectionRepository,
    private readonly _userRepository: GenericUserRepository,
  ) {
    super(CreateActivityWithNotifications.name)
  }

  private async getCollection(collectionId: string) {
    return this._collectionRepository.getById(new UniqueEntityID(collectionId))
  }

  private async getUser(userId: string) {
    return this._userRepository.getById(new UniqueEntityID(userId))
  }

  async execute(request: Readonly<CreateActivityWithNotificationsDTO>): Promise<ActivityDTO> {
    const { type, data, collectionId, targetUsers, actorUserId } = request

    // Validate collection exists
    const collection = await this.getCollection(collectionId)

    // Determine the actor (user who triggered the activity)
    const actorUser = actorUserId 
      ? await this.getUser(actorUserId)
      : AuthContext.getUser()

    // Create the activity
    const activity = new Activity({
      collectionId: collection.id,
      userId: actorUser.id,
      type,
      data,
    })

    await this._activityRepository.save(activity)

    // Create notifications for target users
    const notifications: Notification[] = []
    
    for (const target of targetUsers) {
      try {
        // Verify target user exists and has email notifications enabled
        const targetUser = await this.getUser(target.userId.value)
        
        if (targetUser.emailNotifications) {
          const notification = new Notification({
            userId: target.userId,
            activityId: activity.id,
            read: false,
            sent: false,
          })

          notifications.push(notification)
        }
      } catch (error) {
        // Log error but don't fail the entire operation if a target user doesn't exist
        console.warn(`Failed to create notification for user ${target.userId.value}:`, error)
      }
    }

    // Save all notifications
    if (notifications.length > 0) {
      await this._notificationRepository.saveMany(notifications)

      // Trigger notification events for email sending
      for (const notification of notifications) {
        notification.notify() // This will trigger NotificationCreatedEvent when implemented
      }
    }

    return ActivityMapper.getInstance().toDTO(activity)
  }
}

export { CreateActivityWithNotifications }
