import { collectionRepository } from '../../../collections/repositories'
import { userRepository } from '../../../users/repositories'
import { activityRepository, notificationRepository } from '../../repositories'
import { CreateActivity } from './createActivity'
import { CreateActivityWithNotifications } from './createActivityWithNotifications'

// Original use case for backward compatibility
const createActivityUseCase = new CreateActivity(
  activityRepository,
  collectionRepository,
)

// Enhanced use case for domain events
const createActivityWithNotificationsUseCase = new CreateActivityWithNotifications(
  activityRepository,
  notificationRepository,
  collectionRepository,
  userRepository,
)

export { 
  createActivityUseCase,
  createActivityWithNotificationsUseCase 
}
