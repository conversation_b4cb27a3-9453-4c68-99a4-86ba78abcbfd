import { UniqueEntityID } from '../../../../../shared/domain/uniqueEntityID'
import { stubCollection } from '../../../../collections/domain/__stubs__/collection.stub'
import { GenericCollectionRepository } from '../../../../collections/repositories/interfaces/genericCollectionRepository'
import { stubUser } from '../../../../users/domain/__stubs__/user.stub'
import { GenericUserRepository } from '../../../../users/repositories/interfaces/genericUserRepository'
import { stubActivity } from '../../../domain/__stubs__/activity.stub'
import { stubNotification } from '../../../domain/__stubs__/notification.stub'
import { GenericActivityRepository } from '../../../repositories/interfaces/genericActivityRepository'
import { GenericNotificationRepository } from '../../../repositories/interfaces/genericNotificationRepository'
import { CreateActivityWithNotifications } from '../createActivityWithNotifications'
import { CreateActivityWithNotificationsDTO } from '../createActivityWithNotifications.dto'

describe('CreateActivityWithNotifications', () => {
  let useCase: CreateActivityWithNotifications
  let mockActivityRepository: jest.Mocked<GenericActivityRepository>
  let mockNotificationRepository: jest.Mocked<GenericNotificationRepository>
  let mockCollectionRepository: jest.Mocked<GenericCollectionRepository>
  let mockUserRepository: jest.Mocked<GenericUserRepository>

  beforeEach(() => {
    mockActivityRepository = {
      save: jest.fn(),
    } as any

    mockNotificationRepository = {
      saveMany: jest.fn(),
    } as any

    mockCollectionRepository = {
      getById: jest.fn(),
    } as any

    mockUserRepository = {
      getById: jest.fn(),
    } as any

    useCase = new CreateActivityWithNotifications(
      mockActivityRepository,
      mockNotificationRepository,
      mockCollectionRepository,
      mockUserRepository
    )
  })

  it('should create activity and notifications for target users', async () => {
    const collection = stubCollection()
    const actorUser = stubUser()
    const targetUser1 = stubUser()
    const targetUser2 = stubUser()

    const request: CreateActivityWithNotificationsDTO = {
      type: 'ENTRY_CREATE',
      data: { entryId: 'test-entry-id' },
      collectionId: collection.id.value,
      targetUsers: [
        { userId: targetUser1.id, reason: 'COLLECTION_MANAGER' },
        { userId: targetUser2.id, reason: 'EDITOR' }
      ],
      actorUserId: actorUser.id.value
    }

    mockCollectionRepository.getById.mockResolvedValue(collection)
    mockUserRepository.getById
      .mockResolvedValueOnce(actorUser) // Actor user
      .mockResolvedValueOnce(targetUser1) // Target user 1
      .mockResolvedValueOnce(targetUser2) // Target user 2

    const result = await useCase.execute(request)

    expect(mockCollectionRepository.getById).toHaveBeenCalledWith(collection.id)
    expect(mockUserRepository.getById).toHaveBeenCalledWith(actorUser.id)
    expect(mockActivityRepository.save).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'ENTRY_CREATE',
        data: { entryId: 'test-entry-id' },
        collectionId: collection.id,
        userId: actorUser.id
      })
    )
    expect(mockNotificationRepository.saveMany).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          userId: targetUser1.id,
          read: false,
          sent: false
        }),
        expect.objectContaining({
          userId: targetUser2.id,
          read: false,
          sent: false
        })
      ])
    )
    expect(result).toEqual(
      expect.objectContaining({
        type: 'ENTRY_CREATE',
        collectionId: collection.id.value,
        userId: actorUser.id.value
      })
    )
  })

  it('should skip notifications for users with email notifications disabled', async () => {
    const collection = stubCollection()
    const actorUser = stubUser()
    const targetUserWithNotifications = stubUser()
    const targetUserWithoutNotifications = stubUser({
      settings: { emailNotifications: false }
    })

    const request: CreateActivityWithNotificationsDTO = {
      type: 'ENTRY_UPDATE',
      data: { entryId: 'test-entry-id' },
      collectionId: collection.id.value,
      targetUsers: [
        { userId: targetUserWithNotifications.id, reason: 'COLLECTION_MANAGER' },
        { userId: targetUserWithoutNotifications.id, reason: 'EDITOR' }
      ],
      actorUserId: actorUser.id.value
    }

    mockCollectionRepository.getById.mockResolvedValue(collection)
    mockUserRepository.getById
      .mockResolvedValueOnce(actorUser)
      .mockResolvedValueOnce(targetUserWithNotifications)
      .mockResolvedValueOnce(targetUserWithoutNotifications)

    await useCase.execute(request)

    expect(mockNotificationRepository.saveMany).toHaveBeenCalledWith([
      expect.objectContaining({
        userId: targetUserWithNotifications.id,
        read: false,
        sent: false
      })
    ])
  })

  it('should handle non-existent target users gracefully', async () => {
    const collection = stubCollection()
    const actorUser = stubUser()
    const validTargetUser = stubUser()
    const invalidUserId = new UniqueEntityID()

    const request: CreateActivityWithNotificationsDTO = {
      type: 'ENTRY_DELETE',
      data: { entryId: 'test-entry-id' },
      collectionId: collection.id.value,
      targetUsers: [
        { userId: validTargetUser.id, reason: 'COLLECTION_MANAGER' },
        { userId: invalidUserId, reason: 'EDITOR' }
      ],
      actorUserId: actorUser.id.value
    }

    mockCollectionRepository.getById.mockResolvedValue(collection)
    mockUserRepository.getById
      .mockResolvedValueOnce(actorUser)
      .mockResolvedValueOnce(validTargetUser)
      .mockRejectedValueOnce(new Error('User not found'))

    // Should not throw error
    await expect(useCase.execute(request)).resolves.toBeDefined()

    expect(mockNotificationRepository.saveMany).toHaveBeenCalledWith([
      expect.objectContaining({
        userId: validTargetUser.id,
        read: false,
        sent: false
      })
    ])
  })
})
