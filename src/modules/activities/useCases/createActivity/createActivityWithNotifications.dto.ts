import { NotificationTarget } from '../../domain/events/interfaces/IActivityDomainEvent'

export interface CreateActivityWithNotificationsDTO {
  type: string
  data: unknown
  collectionId: string
  targetUsers: NotificationTarget[]
  /**
   * Optional user ID to override the current authenticated user
   * Used when the activity is triggered by domain events on behalf of another user
   */
  actorUserId?: string
}
