import { UniqueEntityID } from '../../../../shared/domain/uniqueEntityID'
import { logger } from '../../../../shared/infra/logger'
import { UseCase } from '../../../../shared/infra/useCase'
import { GenericUserRepository } from '../../../users/repositories/interfaces/genericUserRepository'
import { GenericActivityRepository } from '../../repositories/interfaces/genericActivityRepository'
import { GenericNotificationRepository } from '../../repositories/interfaces/genericNotificationRepository'
import { SendEmailNotificationDTO } from './sendEmailNotification.dto'

export class SendEmailNotification extends UseCase<SendEmailNotificationDTO, void> {
  constructor(
    private readonly _notificationRepository: GenericNotificationRepository,
    private readonly _activityRepository: GenericActivityRepository,
    private readonly _userRepository: GenericUserRepository,
  ) {
    super('SendEmailNotification')
  }

  public async execute(request: SendEmailNotificationDTO): Promise<void> {
    try {
      const notificationId = new UniqueEntityID(request.notificationId)
      
      // Get the notification
      const notification = await this._notificationRepository.getById(notificationId)
      
      // Skip if already sent
      if (notification.sent) {
        logger.info('Notification already sent, skipping email', {
          notificationId: notification.id.value,
        })
        return
      }

      // Get the user
      const user = await this._userRepository.getById(notification.userId)
      
      // Skip if user has email notifications disabled
      if (!user.emailNotifications) {
        logger.info('User has email notifications disabled, skipping email', {
          notificationId: notification.id.value,
          userId: user.id.value,
        })
        return
      }

      // Get the activity for email content
      const activity = await this._activityRepository.getById(notification.activityId)

      // Log the email sending (in a real implementation, this would send actual emails)
      logger.info('Sending notification email', {
        notificationId: notification.id.value,
        userId: user.id.value,
        userEmail: user.email.value,
        userName: user.name,
        activityType: activity.type,
        activityData: activity.data,
        collectionId: activity.collectionId.value,
      })

      // Mark notification as sent
      notification.setProp('sent', true)
      await this._notificationRepository.save(notification)

      logger.info('Notification email sent successfully', {
        notificationId: notification.id.value,
        userId: user.id.value,
      })

    } catch (error) {
      logger.error('Failed to send notification email', {
        notificationId: request.notificationId,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      // Don't throw - we don't want email failures to break the main business flow
    }
  }
}
