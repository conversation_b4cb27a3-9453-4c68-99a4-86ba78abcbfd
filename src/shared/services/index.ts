import { activitiesServices } from '../../modules/activities/services'
import { collectionsServices } from '../../modules/collections/services'
import { entriesServices } from '../../modules/entries/services'
import { usersServices } from '../../modules/users/services'

const initializeAllServices = async () => {
  await usersServices.initialize()
  await activitiesServices.initialize()
  await collectionsServices.initialize()
  await entriesServices.initialize()
}

export { initializeAllServices }
